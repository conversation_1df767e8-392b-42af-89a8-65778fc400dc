"""
Scraper pro hokej.cz - alternativa k Flashscore
"""

import requests
from bs4 import BeautifulSoup
import json
import re

class HokejCzScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "cs,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive"
        })
    
    def get_teams_and_matches(self):
        """
        Získá týmy a zápasy z hokej.cz
        """
        url = "https://www.hokej.cz/tipsport-extraliga"
        
        try:
            print("🔍 Načítám hokej.cz...")
            r = self.session.get(url)
            r.raise_for_status()
            
            print(f"✅ Stránka načtena, velikost: {len(r.text)} znaků")
            
            soup = BeautifulSoup(r.text, "html.parser")
            
            # Uložíme HTML pro debug
            with open("hokej_cz_debug.html", "w", encoding="utf-8") as f:
                f.write(r.text)
            
            # Hledáme týmy v tabulce
            teams = self.extract_teams(soup)
            
            # Hledáme nadcházející zápasy
            matches = self.extract_upcoming_matches(soup)
            
            return teams, matches
            
        except Exception as e:
            print(f"❌ Chyba při načítání hokej.cz: {e}")
            return {}, []
    
    def extract_teams(self, soup):
        """
        Extrahuje týmy z tabulky
        """
        teams = {}
        
        # Hledáme tabulku s týmy
        tables = soup.find_all("table")
        
        for table in tables:
            rows = table.find_all("tr")
            for row in rows:
                cells = row.find_all(["td", "th"])
                for cell in cells:
                    # Hledáme odkazy na týmy
                    links = cell.find_all("a", href=True)
                    for link in links:
                        href = link.get("href")
                        text = link.get_text(strip=True)
                        
                        # Filtrujeme týmy extraligy
                        if text and len(text) > 3 and ("HC" in text or "BK" in text or any(keyword in text.lower() for keyword in ["sparta", "trinec", "kometa", "brno", "pardubice", "plzen", "liberec", "olomouc", "budejovice", "kladno", "vitkovice", "hradec", "boleslav", "litvinov", "vary"])):
                            full_url = href if href.startswith("http") else f"https://www.hokej.cz{href}"
                            teams[text] = full_url
        
        print(f"✅ Nalezeno {len(teams)} týmů z hokej.cz")
        return teams
    
    def extract_upcoming_matches(self, soup):
        """
        Extrahuje nadcházející zápasy
        """
        matches = []
        
        # Hledáme sekci s nadcházejícími zápasy
        # Různé možné selektory
        selectors = [
            "div[class*='match']",
            "div[class*='game']",
            "div[class*='zapas']",
            ".match",
            ".game",
            ".zapas"
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                print(f"Nalezeno {len(elements)} elementů pro selektor: {selector}")
                
                for elem in elements[:10]:  # Omezíme na prvních 10
                    text = elem.get_text(strip=True)
                    if "vs" in text.lower() or " - " in text:
                        # Pokusíme se parsovat zápas
                        match = self.parse_match_text(text)
                        if match:
                            matches.append(match)
        
        # Pokud nenajdeme zápasy, zkusíme hledat v textu stránky
        if not matches:
            matches = self.find_matches_in_text(soup.get_text())
        
        print(f"✅ Nalezeno {len(matches)} zápasů")
        return matches
    
    def parse_match_text(self, text):
        """
        Parsuje text zápasu ve formátu "Tým A vs Tým B" nebo "Tým A - Tým B"
        """
        # Různé formáty
        patterns = [
            r"(.+?)\s+vs\s+(.+)",
            r"(.+?)\s+-\s+(.+)",
            r"(.+?)\s+–\s+(.+)",
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                home = match.group(1).strip()
                away = match.group(2).strip()
                
                # Filtrujeme pouze hokejové týmy
                if self.is_hockey_team(home) and self.is_hockey_team(away):
                    return (home, away)
        
        return None
    
    def is_hockey_team(self, team_name):
        """
        Zkontroluje, jestli je to hokejový tým
        """
        hockey_keywords = [
            "HC", "BK", "sparta", "trinec", "kometa", "brno", "pardubice", 
            "plzen", "liberec", "olomouc", "budejovice", "kladno", "vitkovice", 
            "hradec", "boleslav", "litvinov", "vary", "oceláři", "tygři", "motor"
        ]
        
        return any(keyword.lower() in team_name.lower() for keyword in hockey_keywords)
    
    def find_matches_in_text(self, text):
        """
        Hledá zápasy v celém textu stránky
        """
        matches = []
        
        # Známé týmy extraligy
        teams = [
            "Sparta Praha", "HC Sparta Praha", "Sparta",
            "HC Oceláři Třinec", "Třinec", "Oceláři",
            "HC Kometa Brno", "Kometa Brno", "Kometa",
            "HC Verva Litvínov", "Litvínov", "Verva",
            "HC Dynamo Pardubice", "Pardubice", "Dynamo",
            "HC Energie Karlovy Vary", "Karlovy Vary", "Energie",
            "Mountfield HK", "Hradec Králové", "HK",
            "BK Mladá Boleslav", "Mladá Boleslav", "Boleslav",
            "HC Plzeň", "Plzeň", "Škoda Plzeň",
            "Bílí Tygři Liberec", "Liberec", "Tygři",
            "HC Olomouc", "Olomouc",
            "HC Motor České Budějovice", "České Budějovice", "Motor",
            "HC Vítkovice Ridera", "Vítkovice", "Ridera",
            "HC Kladno", "Kladno"
        ]
        
        # Hledáme kombinace týmů
        for i, team1 in enumerate(teams):
            for team2 in teams[i+1:]:
                if team1.lower() in text.lower() and team2.lower() in text.lower():
                    # Zkontrolujeme, jestli jsou blízko sebe (možný zápas)
                    team1_pos = text.lower().find(team1.lower())
                    team2_pos = text.lower().find(team2.lower())
                    
                    if abs(team1_pos - team2_pos) < 100:  # Blízko sebe
                        matches.append((team1, team2))
                        break
        
        return matches[:6]  # Max 6 zápasů na kolo

def test_hokej_cz():
    """
    Test scraperu hokej.cz
    """
    print("🧪 TESTOVÁNÍ HOKEJ.CZ SCRAPERU")
    print("=" * 40)
    
    scraper = HokejCzScraper()
    teams, matches = scraper.get_teams_and_matches()
    
    print(f"\n📊 VÝSLEDKY:")
    print(f"Týmy: {len(teams)}")
    for name, url in list(teams.items())[:5]:
        print(f"  - {name}: {url}")
    
    print(f"\nZápasy: {len(matches)}")
    for i, (home, away) in enumerate(matches, 1):
        print(f"  {i}. {home} vs {away}")
    
    return teams, matches

if __name__ == "__main__":
    test_hokej_cz()
