"""
Testovací skript pro ověření funkčnosti scraperu
Spustí se bez OpenAI API klíče - pouze testuje scraping
"""

import requests
from bs4 import BeautifulSoup
import json
from ehl_predictor import EHLPredictor

def test_team_urls():
    """Test načítání URL týmů"""
    print("🧪 Test načítání týmů...")
    predictor = EHLPredictor()
    teams = predictor.get_team_urls()
    
    if teams:
        print(f"✅ Úspěch! Nalezeno {len(teams)} týmů:")
        for name, url in list(teams.items())[:5]:  # Zobrazíme prvních 5
            print(f"   - {name}: {url}")
        if len(teams) > 5:
            print(f"   ... a dalších {len(teams) - 5} týmů")
    else:
        print("❌ Nepodařilo se načíst týmy")
    
    return teams

def test_upcoming_matches():
    """Test načítání nadcházejí<PERSON><PERSON><PERSON> zá<PERSON>ů"""
    print("\n🧪 Test načítání nadcházej<PERSON><PERSON><PERSON><PERSON> z<PERSON>...")
    predictor = EHLPredictor()
    matches = predictor.get_upcoming_round()
    
    if matches:
        print(f"✅ Úspěch! Nalezeno {len(matches)} zápasů:")
        for i, (home, away) in enumerate(matches, 1):
            print(f"   {i}. {home} vs {away}")
    else:
        print("❌ Nepodařilo se načíst zápasy")
    
    return matches

def test_team_form(teams, team_name="Sparta Praha"):
    """Test načítání formy týmu"""
    print(f"\n🧪 Test načítání formy týmu {team_name}...")
    predictor = EHLPredictor()
    predictor.teams = teams
    
    form = predictor.get_team_form(team_name, 5)
    
    if form and form != ["neznámý"] * 5:
        print(f"✅ Úspěch! Forma týmu {team_name}:")
        for i, result in enumerate(form, 1):
            print(f"   {i}. {result}")
    else:
        print(f"❌ Nepodařilo se načíst formu týmu {team_name}")
        print(f"   Výsledek: {form}")
    
    return form

def test_flashscore_structure():
    """Test struktury Flashscore stránky"""
    print("\n🧪 Test struktury Flashscore...")
    
    url = "https://www.flashscore.cz/hokej/cesko/tipsport-extraliga/"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    
    try:
        r = requests.get(url, headers=headers)
        r.raise_for_status()
        soup = BeautifulSoup(r.text, "html.parser")
        
        print(f"✅ Stránka načtena, velikost: {len(r.text)} znaků")
        
        # Hledáme různé možné selektory
        selectors_to_test = [
            "a[href*='/tym/']",
            "a.participant__participantName",
            "div[class*='event'][class*='match']",
            "div.event__match",
            "div.event__match--scheduled",
            "[class*='participant'][class*='home']",
            "[class*='participant'][class*='away']"
        ]
        
        for selector in selectors_to_test:
            elements = soup.select(selector)
            print(f"   Selektor '{selector}': {len(elements)} elementů")
            
            if elements and len(elements) > 0:
                # Zobrazíme první element jako ukázku
                first_elem = elements[0]
                text = first_elem.get_text(strip=True)[:50]
                print(f"     Ukázka: '{text}...'")
        
    except Exception as e:
        print(f"❌ Chyba při načítání stránky: {e}")

def main():
    """Hlavní testovací funkce"""
    print("🧪 TESTOVÁNÍ EHL PREDICTOR SCRAPERU")
    print("=" * 50)
    
    # Test struktury stránky
    test_flashscore_structure()
    
    # Test načítání týmů
    teams = test_team_urls()
    
    # Test načítání zápasů
    matches = test_upcoming_matches()
    
    # Test formy týmu (pokud se podařilo načíst týmy)
    if teams:
        # Zkusíme několik různých názvů týmů
        test_teams = ["Sparta Praha", "HC Oceláři Třinec", "HC Kometa Brno"]
        for team in test_teams:
            if team in teams:
                test_team_form(teams, team)
                break
    
    print("\n" + "=" * 50)
    print("🏁 Testování dokončeno!")
    print("\nPokud vidíte ❌ chyby, může to být způsobeno:")
    print("- Změnou struktury Flashscore stránky")
    print("- Dočasnou nedostupností webu")
    print("- Blokováním požadavků")
    print("\nV takovém případě bude potřeba aktualizovat selektory v kódu.")

if __name__ == "__main__":
    main()
