@echo off
echo 🏒 EHL Predictor - Spouštění...
echo ================================

REM Zkontroluj, jestli je nastaven API klíč
if "%OPENAI_API_KEY%"=="" (
    echo ❌ CHYBA: Není nastaven OPENAI_API_KEY
    echo.
    echo Nastavte API klíč pomocí:
    echo set OPENAI_API_KEY=your_api_key_here
    echo.
    pause
    exit /b 1
)

REM Zkontroluj, jestli existuje Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CHYBA: Python není nainstalován nebo není v PATH
    echo.
    echo Nainstalujte Python z https://python.org
    echo.
    pause
    exit /b 1
)

REM Zkontroluj závislosti
echo 📦 Kontrolujem závislosti...
pip show requests >nul 2>&1
if errorlevel 1 (
    echo 📥 Instalujem závislosti...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ CHYBA: Nepodařilo se nainstalovat závislosti
        pause
        exit /b 1
    )
)

REM Spusť predikci
echo.
echo 🚀 Spouštím predikci...
echo.
python ehl_predictor.py

echo.
echo 🏁 Hotovo!
pause
