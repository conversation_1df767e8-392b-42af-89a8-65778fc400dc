# 🏒 EHL Predictor - Predikční bot pro českou extraligu

Automatický predikční systém pro českou hokejovou extraligu, který předvídá výsledky zápasů na základě:
- ✅ Aktuální formy týmů (posledních 5 zápasů)
- ✅ Výhody domácího prostředí
- ✅ AI analýzy pomocí GPT-4
- 🔄 Produktivity hráčů (v přípravě)
- 🔄 Zranění a absencí (v přípravě)

## 🚀 Rychlý start

### 1. Instalace závislostí
```bash
pip install -r requirements.txt
```

### 2. Nastavení OpenAI API klíče
```bash
# Windows
set OPENAI_API_KEY=your_api_key_here

# Linux/Mac
export OPENAI_API_KEY=your_api_key_here
```

### 3. Spuštěn<PERSON> predikce
```bash
python ehl_predictor.py
```

## 📊 Výstup

Program vytvoří:
- **Konzolový výstup** s přehlednými predikcemi
- **JSON soubor** s detailními daty (`predikce_YYYYMMDD_HHMMSS.json`)

### Příklad výstupu:
```
🏒 PREDIKCE CELÉHO KOLA - TIPSPORT EXTRALIGA
============================================================

📊 ZÁPAS 1: Sparta Praha vs HC Oceláři Třinec
----------------------------------------
🎯 Pravděpodobnost výhry:
   Sparta Praha: 62%
   HC Oceláři Třinec: 38%
⚽ Odhad skóre: 3:2
💡 Důvody predikce:
   1. Sparta má sérii 4 výher za sebou
   2. Výhoda domácího prostředí
   3. Třinci chybí klíčoví hráči
```

## 🔧 Konfigurace

Upravte `config.py` pro:
- Počet analyzovaných zápasů (`NUM_MATCHES`)
- Kreativitu AI (`TEMPERATURE`)
- Váhy jednotlivých faktorů (`PREDICTION_WEIGHTS`)

## 📁 Struktura projektu

```
EHL/
├── ehl_predictor.py    # Hlavní skript
├── config.py           # Konfigurace
├── requirements.txt    # Závislosti
├── README.md          # Dokumentace
└── predictions/       # Uložené predikce (vytvoří se automaticky)
```

## 🛠️ Jak to funguje

1. **Scraping dat** - Stahuje aktuální výsledky z Flashscore.cz
2. **Analýza formy** - Vyhodnocuje posledních 5 zápasů každého týmu
3. **AI predikce** - GPT-4 analyzuje data a vytvoří predikci
4. **Výstup** - Zobrazí pravděpodobnosti, skóre a důvody

## ⚠️ Důležité poznámky

- **API klíč**: Potřebujete platný OpenAI API klíč
- **Rate limiting**: Program má zabudované pauzy mezi požadavky
- **Scraping**: Flashscore může změnit strukturu → může být potřeba aktualizace
- **Přesnost**: Jde o predikce, ne záruky! Hokej je nepredvídatelný 🏒

## 🔮 Plánované funkce

- [ ] Analýza zranění hráčů
- [ ] Statistiky produktivity hráčů
- [ ] Historická úspěšnost predikcí
- [ ] Web interface
- [ ] Telegram bot
- [ ] Analýza brankářů
- [ ] Počasí a další faktory

## 🐛 Řešení problémů

### Chyba "Nepodařilo se načíst týmy"
- Zkontrolujte internetové připojení
- Flashscore může být dočasně nedostupný
- Zkuste spustit znovu za chvíli

### Chyba "Není nastaven OPENAI_API_KEY"
- Nastavte proměnnou prostředí s vaším API klíčem
- Zkontrolujte, že klíč je platný

### Nesprávné predikce
- Zkontrolujte, že se načítají správná data
- Upravte `TEMPERATURE` v config.py pro konzervativnější predikce

## 📞 Podpora

Máte problém nebo nápad na vylepšení? Napište issue nebo pull request!

---
*Vytvořeno s ❤️ pro fanoušky české hokejové extraligy*
