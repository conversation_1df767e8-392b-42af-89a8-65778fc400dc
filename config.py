"""
Konfigurační soubor pro EHL Predictor
"""

# Základní nastavení
NUM_MATCHES = 5  # Kolik posledních z<PERSON><PERSON> brát pro analýzu formy
TEMPERATURE = 0.3  # Kreativita LLM (0.0 = konzervativní, 1.0 = kreativní)
MAX_TOKENS = 500  # Maximální délka odpovědi LLM

# URL adresy
EXTRALIGA_URL = "https://www.flashscore.cz/hokej/cesko/tipsport-extraliga/"
HOKEJ_CZ_URL = "https://www.hokej.cz/tipsport-extraliga"

# HTTP hlavičky pro scraping
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "cs,en-US;q=0.7,en;q=0.3",
    "Accept-Encoding": "gzip, deflate",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1"
}

# Mapování názvů týmů (pro případné rozdíly mezi zdroji)
TEAM_MAPPING = {
    "Sparta Praha": ["HC Sparta Praha", "Sparta", "SPR"],
    "HC Oceláři Třinec": ["Třinec", "Oceláři", "TRI"],
    "HC Kometa Brno": ["Kometa Brno", "Brno", "KOM"],
    "HC Verva Litvínov": ["Litvínov", "Verva", "LIT"],
    "HC Dynamo Pardubice": ["Pardubice", "Dynamo", "PAR"],
    "HC Energie Karlovy Vary": ["Karlovy Vary", "Energie", "KAR"],
    "Mountfield HK": ["Hradec Králové", "HK", "HRA"],
    "BK Mladá Boleslav": ["Mladá Boleslav", "Boleslav", "BOL"],
    "HC Plzeň": ["Plzeň", "Škoda Plzeň", "PLZ"],
    "Bílí Tygři Liberec": ["Liberec", "Tygři", "LIB"],
    "HC Olomouc": ["Olomouc", "OLO"],
    "HC Motor České Budějovice": ["České Budějovice", "Motor", "BUD"],
    "HC Vítkovice Ridera": ["Vítkovice", "Ridera", "VIT"],
    "HC Kladno": ["Kladno", "KLA"]
}

# Váhy pro různé faktory v predikci
PREDICTION_WEIGHTS = {
    "home_advantage": 0.15,  # Výhoda domácího prostředí (15%)
    "recent_form": 0.40,     # Nedávná forma (40%)
    "head_to_head": 0.20,    # Vzájemné zápasy (20%)
    "injuries": 0.15,        # Zranění (15%)
    "random_factor": 0.10    # Náhodný faktor/překvapení (10%)
}

# Nastavení pro retry mechanismus
MAX_RETRIES = 3
RETRY_DELAY = 2  # sekundy

# Nastavení pro ukládání dat
SAVE_PREDICTIONS = True
PREDICTIONS_DIR = "predictions"
DATA_DIR = "data"
