"""
Debug skript pro analýzu struktury Flashscore stránky
"""

import requests
from bs4 import BeautifulSoup
import re

def analyze_flashscore():
    url = "https://www.flashscore.cz/hokej/cesko/tipsport-extraliga/"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "cs,en-US;q=0.7,en;q=0.3",
        "Accept-Encoding": "gzip, deflate",
        "Connection": "keep-alive"
    }
    
    try:
        print("🔍 Načítám Flashscore stránku...")
        r = requests.get(url, headers=headers)
        r.raise_for_status()
        
        print(f"✅ Stránka načtena, velikost: {len(r.text)} znaků")
        print(f"Status code: {r.status_code}")
        
        soup = BeautifulSoup(r.text, "html.parser")
        
        # Uložíme HTML pro analýzu
        with open("flashscore_debug.html", "w", encoding="utf-8") as f:
            f.write(r.text)
        print("💾 HTML uloženo do flashscore_debug.html")
        
        # Hledáme všechny odkazy
        print("\n🔗 Analýza odkazů:")
        all_links = soup.find_all("a", href=True)
        team_links = []
        
        for link in all_links:
            href = link.get("href", "")
            text = link.get_text(strip=True)
            
            # Hledáme odkazy na týmy
            if "/tym/" in href or "team" in href.lower():
                team_links.append((text, href))
            
            # Hledáme odkazy obsahující názvy týmů
            team_keywords = ["sparta", "trinec", "kometa", "brno", "pardubice", "plzen", "liberec"]
            if any(keyword in text.lower() for keyword in team_keywords):
                team_links.append((text, href))
        
        print(f"Nalezeno {len(team_links)} potenciálních týmových odkazů:")
        for text, href in team_links[:10]:  # Zobrazíme prvních 10
            print(f"  - '{text}' -> {href}")
        
        # Hledáme div elementy s class obsahující "event" nebo "match"
        print("\n📊 Analýza zápasů:")
        event_divs = soup.find_all("div", class_=re.compile(r"event|match", re.I))
        print(f"Nalezeno {len(event_divs)} div elementů s 'event' nebo 'match' v class")
        
        for i, div in enumerate(event_divs[:5]):  # Zobrazíme prvních 5
            classes = div.get("class", [])
            text = div.get_text(strip=True)[:100]
            print(f"  {i+1}. Classes: {classes}")
            print(f"     Text: '{text}...'")
        
        # Hledáme tabulky
        print("\n📋 Analýza tabulek:")
        tables = soup.find_all("table")
        print(f"Nalezeno {len(tables)} tabulek")
        
        for i, table in enumerate(tables[:3]):
            rows = table.find_all("tr")
            print(f"  Tabulka {i+1}: {len(rows)} řádků")
            if rows:
                first_row_text = rows[0].get_text(strip=True)[:100]
                print(f"    První řádek: '{first_row_text}...'")
        
        # Hledáme script tagy (možná jsou data v JS)
        print("\n🔧 Analýza script tagů:")
        scripts = soup.find_all("script")
        print(f"Nalezeno {len(scripts)} script tagů")
        
        for script in scripts:
            if script.string and ("extraliga" in script.string.lower() or "sparta" in script.string.lower()):
                print("  Nalezen script s relevantními daty!")
                print(f"    Délka: {len(script.string)} znaků")
                # Uložíme první relevantní script
                with open("flashscore_script.js", "w", encoding="utf-8") as f:
                    f.write(script.string)
                print("    💾 Script uložen do flashscore_script.js")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

if __name__ == "__main__":
    analyze_flashscore()
