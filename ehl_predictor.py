"""
EHL (Extraliga Hockey League) Predictor Bot
Predikuje výsledky zápasů české hokejové extraligy na základě:
- Aktuální formy týmů
- Produktivity hráčů
- Zranění a absencí
"""

import requests
from bs4 import BeautifulSoup
from openai import OpenAI
import json
import os
from datetime import datetime
import time
from typing import List, Dict, Tuple, Optional

# --- CONFIG ---
client = OpenAI()
NUM_MATCHES = 5  # kolik posledních zápasů brát pro formu
EXTRALIGA_URL = "https://www.flashscore.cz/hokej/cesko/tipsport-extraliga/"
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

class EHLPredictor:
    def __init__(self):
        self.teams = {}
        self.session = requests.Session()
        self.session.headers.update(HEADERS)
    
    def get_team_urls(self) -> Dict[str, str]:
        """
        Získá URL všech týmů z extraligy.
        """
        print("🔍 Načítám seznam týmů z extraligy...")
        try:
            r = self.session.get(EXTRALIGA_URL)
            r.raise_for_status()
            soup = BeautifulSoup(r.text, "html.parser")

            teams = {}
            # Hledáme odkazy na týmy v tabulce
            for a in soup.select("a[href*='/tym/']"):
                name = a.get_text(strip=True)
                if name and len(name) > 2:  # filtrujeme prázdné názvy
                    url = "https://www.flashscore.cz" + a["href"] if a["href"].startswith("/") else a["href"]
                    teams[name] = url
            
            print(f"✅ Nalezeno {len(teams)} týmů")
            return teams
        except Exception as e:
            print(f"❌ Chyba při načítání týmů: {e}")
            return {}

    def get_upcoming_round(self) -> List[Tuple[str, str]]:
        """
        Najde nejbližší kolo extraligy a vrátí seznam zápasů [(domácí, hosté)].
        """
        print("📅 Zjišťuji nadcházející kolo...")
        try:
            r = self.session.get(EXTRALIGA_URL)
            r.raise_for_status()
            soup = BeautifulSoup(r.text, "html.parser")

            matches = []
            # Hledáme naplánované zápasy
            for m in soup.select("div[class*='event'][class*='match']")[:10]:
                home_el = m.select_one("[class*='participant'][class*='home']")
                away_el = m.select_one("[class*='participant'][class*='away']")
                
                if home_el and away_el:
                    home = home_el.get_text(strip=True)
                    away = away_el.get_text(strip=True)
                    if home and away:
                        matches.append((home, away))
            
            print(f"✅ Nalezeno {len(matches)} zápasů v nadcházejícím kole")
            return matches[:7]  # max 7 zápasů na kolo
        except Exception as e:
            print(f"❌ Chyba při načítání kola: {e}")
            # Fallback - ukázkové kolo
            return [
                ("Sparta Praha", "HC Oceláři Třinec"),
                ("HC Kometa Brno", "HC Verva Litvínov"),
                ("HC Dynamo Pardubice", "HC Energie Karlovy Vary"),
                ("Mountfield HK", "BK Mladá Boleslav"),
                ("HC Plzeň", "Bílí Tygři Liberec"),
                ("HC Olomouc", "HC Motor České Budějovice")
            ]

    def get_team_form(self, team_name: str, num_matches: int = NUM_MATCHES) -> List[str]:
        """
        Načte poslední výsledky týmu (výhry/prohry).
        """
        if team_name not in self.teams:
            print(f"⚠️ Tým {team_name} nenalezen v seznamu")
            return ["neznámý"] * num_matches
        
        try:
            team_url = self.teams[team_name]
            r = self.session.get(team_url)
            r.raise_for_status()
            soup = BeautifulSoup(r.text, "html.parser")

            results = []
            # Hledáme dokončené zápasy
            matches = soup.select("div[class*='event'][class*='match']")
            
            for m in matches:
                score_el = m.select_one("[class*='score']")
                if not score_el:
                    continue
                    
                score_text = score_el.get_text(strip=True)
                if ":" not in score_text:
                    continue
                    
                home_el = m.select_one("[class*='participant'][class*='home']")
                away_el = m.select_one("[class*='participant'][class*='away']")
                
                if not (home_el and away_el):
                    continue
                    
                home = home_el.get_text(strip=True)
                away = away_el.get_text(strip=True)

                try:
                    # Parsujeme skóre
                    score_parts = score_text.split(":")
                    h_score = int(score_parts[0].strip())
                    a_score = int(score_parts[1].strip())
                    
                    # Určíme výsledek pro náš tým
                    if team_name.lower() in home.lower():
                        result = "výhra" if h_score > a_score else "prohra"
                    elif team_name.lower() in away.lower():
                        result = "výhra" if a_score > h_score else "prohra"
                    else:
                        continue
                    
                    results.append(result)
                    
                    if len(results) >= num_matches:
                        break
                        
                except (ValueError, IndexError):
                    continue

            # Doplníme neznámé výsledky, pokud jich nemáme dost
            while len(results) < num_matches:
                results.append("neznámý")
                
            return results[:num_matches]
            
        except Exception as e:
            print(f"⚠️ Chyba při načítání formy týmu {team_name}: {e}")
            return ["neznámý"] * num_matches

    def predict_match(self, home: str, away: str, form_home: List[str], form_away: List[str]) -> Dict:
        """
        Vytvoří predikci zápasu pomocí LLM.
        """
        prompt = f"""
Jsi sportovní analytik a expert na českou hokejovou extraligu.
Na základě dat předpověz výsledek zápasu.

DATA:
- Domácí: {home}
- Hosté: {away}
- Forma domácího týmu (posl. {NUM_MATCHES}): {', '.join(form_home)}
- Forma hostů (posl. {NUM_MATCHES}): {', '.join(form_away)}

INSTRUKCE:
- Zvaž výhodu domácího prostředí (obvykle +10-15% šance na výhru)
- Forma týmu je klíčová - série výher/proher ovlivňuje morálku
- Extraliga je vyrovnaná soutěž, překvapení jsou častá

Výstup napiš POUZE jako validní JSON ve formátu:
{{
  "zapas": "{home} vs {away}",
  "pravdepodobnost": {{
    "{home}": číslo_0_100,
    "{away}": číslo_0_100
  }},
  "odhad_skore": "X:Y",
  "duvody": ["stručný důvod 1", "stručný důvod 2", "stručný důvod 3"]
}}
"""

        try:
            response = client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "Jsi expert na českou hokejovou extraligu. Odpovídáš POUZE validním JSON formátem."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=500
            )
            
            result = response.choices[0].message.content.strip()
            # Pokusíme se parsovat JSON
            return json.loads(result)
            
        except json.JSONDecodeError as e:
            print(f"⚠️ Chyba při parsování JSON odpovědi: {e}")
            # Fallback predikce
            return {
                "zapas": f"{home} vs {away}",
                "pravdepodobnost": {home: 55, away: 45},
                "odhad_skore": "2:1",
                "duvody": ["Výhoda domácího prostředí", "Chyba při analýze dat"]
            }
        except Exception as e:
            print(f"⚠️ Chyba při volání LLM: {e}")
            return {
                "zapas": f"{home} vs {away}",
                "pravdepodobnost": {home: 50, away: 50},
                "odhad_skore": "1:1",
                "duvody": ["Nedostupná data pro analýzu"]
            }

    def predict_round(self) -> List[Dict]:
        """
        Predikuje celé kolo extraligy.
        """
        print("🚀 Spouštím predikci celého kola...")
        
        # Načteme týmy a kolo
        self.teams = self.get_team_urls()
        if not self.teams:
            print("❌ Nepodařilo se načíst týmy")
            return []
        
        kolo = self.get_upcoming_round()
        if not kolo:
            print("❌ Nepodařilo se načíst zápasy")
            return []
        
        predictions = []
        for i, (home, away) in enumerate(kolo, 1):
            print(f"🟦 [{i}/{len(kolo)}] Predikuji {home} vs {away}...")
            
            # Načteme formu týmů
            form_home = self.get_team_form(home, NUM_MATCHES)
            form_away = self.get_team_form(away, NUM_MATCHES)
            
            # Vytvoříme predikci
            pred = self.predict_match(home, away, form_home, form_away)
            predictions.append(pred)
            
            # Krátká pauza mezi požadavky
            time.sleep(1)
        
        return predictions

    def print_predictions(self, predictions: List[Dict]):
        """
        Vytiskne predikce v přehledném formátu.
        """
        print("\n" + "="*60)
        print("🏒 PREDIKCE CELÉHO KOLA - TIPSPORT EXTRALIGA")
        print("="*60)
        
        for i, p in enumerate(predictions, 1):
            print(f"\n📊 ZÁPAS {i}: {p['zapas']}")
            print("-" * 40)
            
            # Pravděpodobnosti
            teams = list(p['pravdepodobnost'].keys())
            prob1 = p['pravdepodobnost'][teams[0]]
            prob2 = p['pravdepodobnost'][teams[1]]
            
            print(f"🎯 Pravděpodobnost výhry:")
            print(f"   {teams[0]}: {prob1}%")
            print(f"   {teams[1]}: {prob2}%")
            
            print(f"⚽ Odhad skóre: {p['odhad_skore']}")
            
            print(f"💡 Důvody predikce:")
            for j, duvod in enumerate(p['duvody'], 1):
                print(f"   {j}. {duvod}")

def main():
    """
    Hlavní funkce programu.
    """
    print("🏒 EHL PREDICTOR - Predikce české hokejové extraligy")
    print("=" * 55)
    
    # Zkontrolujeme API klíč
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Chyba: Není nastaven OPENAI_API_KEY")
        print("Nastavte proměnnou prostředí: set OPENAI_API_KEY=your_api_key")
        return
    
    predictor = EHLPredictor()
    predictions = predictor.predict_round()
    
    if predictions:
        predictor.print_predictions(predictions)
        
        # Uložíme výsledky do souboru
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"predikce_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(predictions, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 Predikce uloženy do souboru: {filename}")
    else:
        print("❌ Nepodařilo se vytvořit žádné predikce")

if __name__ == "__main__":
    main()
