"""
EHL (Extraliga Hockey League) Predictor Bot - OFFLINE verze
Predikuje výsledky zápasů české hokejové extraligy bez OpenAI API
Používá vlastní algoritmus na základě:
- Aktuální formy týmů
- <PERSON><PERSON><PERSON> tý<PERSON>
- V<PERSON><PERSON><PERSON> domácího prostředí
"""

import requests
from bs4 import BeautifulSoup
import json
import os
from datetime import datetime
import time
import random
from typing import List, Dict, Tuple

# --- CONFIG ---
NUM_MATCHES = 5  # kolik posledních zápasů brát pro formu
EXTRALIGA_URL = "https://www.hokej.cz/tipsport-extraliga"
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "cs,en;q=0.5"
}

class EHLPredictorOffline:
    def __init__(self):
        self.teams = {}
        self.session = requests.Session()
        self.session.headers.update(HEADERS)
        
        # Síla týmů (na základě historických výsledků a současné formy)
        self.team_strength = {
            "sparta": 85, "trinec": 90, "kometa": 75, "brno": 75,
            "pardubice": 80, "plzen": 75, "liberec": 70, "hradec": 65,
            "boleslav": 60, "litvinov": 65, "vary": 55, "olomouc": 60,
            "budejovice": 55, "vitkovice": 65, "kladno": 50
        }
    
    def get_team_strength(self, team_name: str) -> int:
        """
        Vrátí sílu týmu (0-100)
        """
        for keyword, strength in self.team_strength.items():
            if keyword in team_name.lower():
                return strength
        return 65  # default střední síla
    
    def get_team_urls(self) -> Dict[str, str]:
        """
        Získá URL všech týmů z extraligy z hokej.cz
        """
        print("🔍 Načítám seznam týmů z extraligy...")
        try:
            r = self.session.get(EXTRALIGA_URL)
            r.raise_for_status()
            soup = BeautifulSoup(r.text, "html.parser")

            teams = {}
            # Hledáme odkazy na týmy
            for a in soup.find_all("a", href=True):
                href = a.get("href")
                text = a.get_text(strip=True)
                
                # Filtrujeme týmy extraligy
                if (text and len(text) > 3 and 
                    ("HC" in text or "BK" in text or 
                     any(keyword in text.lower() for keyword in [
                         "sparta", "trinec", "kometa", "brno", "pardubice", 
                         "plzen", "liberec", "olomouc", "budejovice", "kladno", 
                         "vitkovice", "hradec", "boleslav", "litvinov", "vary"
                     ]))):
                    full_url = href if href.startswith("http") else f"https://www.hokej.cz{href}"
                    teams[text] = full_url
            
            print(f"✅ Nalezeno {len(teams)} týmů")
            return teams
        except Exception as e:
            print(f"❌ Chyba při načítání týmů: {e}")
            return {}

    def get_upcoming_round(self) -> List[Tuple[str, str]]:
        """
        Najde nejbližší kolo extraligy a vrátí seznam zápasů [(domácí, hosté)].
        """
        print("📅 Zjišťuji nadcházející kolo...")
        
        # Vytvoříme ukázkové kolo s reálnými týmy
        sample_matches = [
            ("HC Sparta Praha", "HC Oceláři Třinec"),
            ("HC Kometa Brno", "HC Verva Litvínov"),
            ("HC Dynamo Pardubice", "HC Energie Karlovy Vary"),
            ("Mountfield HK", "BK Mladá Boleslav"),
            ("HC Škoda Plzeň", "Bílí Tygři Liberec"),
            ("HC Olomouc", "HC Motor České Budějovice"),
            ("HC VÍTKOVICE RIDERA", "HC Kladno")
        ]
        
        print(f"✅ Použito ukázkové kolo s {len(sample_matches)} zápasy")
        return sample_matches

    def get_team_form(self, team_name: str, num_matches: int = NUM_MATCHES) -> List[str]:
        """
        Načte poslední výsledky týmu (výhry/prohry).
        Pro demo účely vrátí simulovanou formu.
        """
        print(f"📊 Načítám formu týmu {team_name}...")
        
        # Pro demo účely vytvoříme simulovanou formu na základě síly týmu
        random.seed(hash(team_name) % 1000)  # Konzistentní "náhodnost" pro každý tým
        
        team_strength = self.get_team_strength(team_name)
        win_prob = team_strength / 100.0  # Převedeme na pravděpodobnost
        
        # Vygenerujeme formu
        results = []
        for _ in range(num_matches):
            if random.random() < win_prob:
                results.append("výhra")
            else:
                results.append("prohra")
        
        print(f"✅ Forma týmu {team_name}: {', '.join(results)}")
        return results

    def calculate_form_score(self, form: List[str]) -> float:
        """
        Vypočítá skóre formy (0.0 - 1.0)
        """
        if not form:
            return 0.5
        
        wins = form.count("výhra")
        total = len(form)
        
        # Dáváme větší váhu novějším zápasům
        weighted_score = 0
        total_weight = 0
        
        for i, result in enumerate(form):
            weight = i + 1  # Novější zápasy mají větší váhu
            if result == "výhra":
                weighted_score += weight
            total_weight += weight
        
        return weighted_score / total_weight if total_weight > 0 else 0.5

    def predict_match(self, home: str, away: str, form_home: List[str], form_away: List[str]) -> Dict:
        """
        Vytvoří predikci zápasu pomocí vlastního algoritmu.
        """
        print(f"🧠 Analyzuji zápas {home} vs {away}...")
        
        # Základní síla týmů
        home_strength = self.get_team_strength(home)
        away_strength = self.get_team_strength(away)
        
        # Forma týmů
        home_form_score = self.calculate_form_score(form_home)
        away_form_score = self.calculate_form_score(form_away)
        
        # Výhoda domácího prostředí (10-15%)
        home_advantage = 12
        
        # Celkové skóre
        home_total = home_strength + (home_form_score * 20) + home_advantage
        away_total = away_strength + (away_form_score * 20)
        
        # Převedeme na pravděpodobnosti
        total_score = home_total + away_total
        home_prob = int((home_total / total_score) * 100)
        away_prob = 100 - home_prob
        
        # Odhad skóre na základě síly týmů
        home_goals = max(1, int((home_total / 100) * 3))
        away_goals = max(0, int((away_total / 100) * 3))
        
        # Přidáme trochu náhodnosti
        random.seed(hash(home + away) % 1000)
        if random.random() < 0.3:  # 30% šance na překvapení
            if random.random() < 0.5:
                home_goals += 1
            else:
                away_goals += 1
        
        # Důvody predikce
        reasons = []
        
        if home_prob > 60:
            reasons.append(f"{home} má výrazně lepší formu a sílu")
        elif home_prob > 55:
            reasons.append(f"{home} má mírnou výhodu")
        elif away_prob > 60:
            reasons.append(f"{away} je favoritem navzdory nevýhodě hostů")
        else:
            reasons.append("Vyrovnaný zápas, může rozhodnout forma dne")
        
        if home_advantage > 10:
            reasons.append("Výhoda domácího prostředí")
        
        # Analýza formy
        home_wins = form_home.count("výhra")
        away_wins = form_away.count("výhra")
        
        if home_wins >= 4:
            reasons.append(f"{home} má skvělou formu ({home_wins}/5 výher)")
        elif home_wins <= 1:
            reasons.append(f"{home} má špatnou formu ({home_wins}/5 výher)")
        
        if away_wins >= 4:
            reasons.append(f"{away} má skvělou formu ({away_wins}/5 výher)")
        elif away_wins <= 1:
            reasons.append(f"{away} má špatnou formu ({away_wins}/5 výher)")
        
        if not reasons:
            reasons.append("Standardní analýza na základě síly týmů")
        
        return {
            "zapas": f"{home} vs {away}",
            "pravdepodobnost": {
                home: home_prob,
                away: away_prob
            },
            "odhad_skore": f"{home_goals}:{away_goals}",
            "duvody": reasons[:3]  # Max 3 důvody
        }

    def predict_round(self) -> List[Dict]:
        """
        Predikuje celé kolo extraligy.
        """
        print("🚀 Spouštím predikci celého kola...")
        
        # Načteme týmy a kolo
        self.teams = self.get_team_urls()
        kolo = self.get_upcoming_round()
        
        predictions = []
        for i, (home, away) in enumerate(kolo, 1):
            print(f"🟦 [{i}/{len(kolo)}] Predikuji {home} vs {away}...")
            
            # Načteme formu týmů
            form_home = self.get_team_form(home, NUM_MATCHES)
            form_away = self.get_team_form(away, NUM_MATCHES)
            
            # Vytvoříme predikci
            pred = self.predict_match(home, away, form_home, form_away)
            predictions.append(pred)
            
            # Krátká pauza mezi analýzami
            time.sleep(0.5)
        
        return predictions

    def print_predictions(self, predictions: List[Dict]):
        """
        Vytiskne predikce v přehledném formátu.
        """
        print("\n" + "="*60)
        print("🏒 PREDIKCE CELÉHO KOLA - TIPSPORT EXTRALIGA")
        print("🤖 OFFLINE VERZE - Vlastní algoritmus")
        print("="*60)
        
        for i, p in enumerate(predictions, 1):
            print(f"\n📊 ZÁPAS {i}: {p['zapas']}")
            print("-" * 40)
            
            # Pravděpodobnosti
            teams = list(p['pravdepodobnost'].keys())
            prob1 = p['pravdepodobnost'][teams[0]]
            prob2 = p['pravdepodobnost'][teams[1]]
            
            print(f"🎯 Pravděpodobnost výhry:")
            print(f"   {teams[0]}: {prob1}%")
            print(f"   {teams[1]}: {prob2}%")
            
            print(f"⚽ Odhad skóre: {p['odhad_skore']}")
            
            print(f"💡 Důvody predikce:")
            for j, duvod in enumerate(p['duvody'], 1):
                print(f"   {j}. {duvod}")

def main():
    """
    Hlavní funkce programu.
    """
    print("🏒 EHL PREDICTOR OFFLINE - Predikce české hokejové extraligy")
    print("🤖 Verze bez OpenAI API - Vlastní algoritmus")
    print("=" * 65)
    
    predictor = EHLPredictorOffline()
    predictions = predictor.predict_round()
    
    if predictions:
        predictor.print_predictions(predictions)
        
        # Uložíme výsledky do souboru
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"predikce_offline_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(predictions, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 Predikce uloženy do souboru: {filename}")
        
        # Statistiky
        print(f"\n📈 STATISTIKY:")
        total_matches = len(predictions)
        print(f"   Celkem zápasů: {total_matches}")
        
        # Spočítáme průměrnou pravděpodobnost favoritů
        avg_favorite_prob = 0
        for p in predictions:
            probs = list(p['pravdepodobnost'].values())
            avg_favorite_prob += max(probs)
        avg_favorite_prob /= total_matches
        
        print(f"   Průměrná pravděpodobnost favorita: {avg_favorite_prob:.1f}%")
        print(f"   Vyrovnanost kola: {'Vysoká' if avg_favorite_prob < 60 else 'Střední' if avg_favorite_prob < 70 else 'Nízká'}")
        
    else:
        print("❌ Nepodařilo se vytvořit žádné predikce")

if __name__ == "__main__":
    main()
